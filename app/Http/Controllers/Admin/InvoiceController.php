<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Invoice;
use App\Services\InvoiceService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class InvoiceController extends Controller
{
    protected $invoiceService;

    public function __construct(InvoiceService $invoiceService)
    {
        $this->invoiceService = $invoiceService;
    }

    /**
     * Generate invoice for an order
     */
    public function generate(Order $order)
    {
        try {
            // Generate invoice
            $invoice = $this->invoiceService->generateInvoice($order);

            // Generate PDF
            $this->invoiceService->generatePdf($invoice);

            return redirect()->back()->with('success', 'Invoice generated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to generate invoice: ' . $e->getMessage());
        }
    }

    /**
     * Download invoice PDF
     */
    public function download(Order $order)
    {
        try {
            $invoice = $order->invoice;
            dd($invoice);

            if (!$invoice) {
                return redirect()->back()->with('error', 'No invoice found for this order.');
            }

            return $this->invoiceService->downloadPdf($invoice);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to download invoice: ' . $e->getMessage());
        }
    }

    /**
     * Generate and download invoice in one action
     */
    public function generateAndDownload(Order $order)
    {
        try {
            // Generate invoice if it doesn't exist
            $invoice = $order->invoice;
            if (!$invoice) {
                $invoice = $this->invoiceService->generateInvoice($order);
            }

            // Generate PDF if it doesn't exist
            if (!$invoice->hasPdf()) {
                $this->invoiceService->generatePdf($invoice);
            }

            return $this->invoiceService->downloadPdf($invoice);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to generate and download invoice: ' . $e->getMessage());
        }
    }

    /**
     * Show invoice details
     */
    public function show(Order $order)
    {
        $invoice = $order->invoice;

        if (!$invoice) {
            return redirect()->back()->with('error', 'No invoice found for this order.');
        }

        return view('admin.invoices.show', compact('invoice', 'order'));
    }

    /**
     * Regenerate invoice PDF
     */
    public function regeneratePdf(Order $order)
    {
        try {
            $invoice = $order->invoice;

            if (!$invoice) {
                return redirect()->back()->with('error', 'No invoice found for this order.');
            }

            // Force regenerate PDF
            $this->invoiceService->generatePdf($invoice);

            return redirect()->back()->with('success', 'Invoice PDF regenerated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to regenerate PDF: ' . $e->getMessage());
        }
    }
}
