<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Category;
use App\Models\Producent;
use App\Models\Property;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class ProductController extends AdminController
{

    public function index()
    {
        $categories = $this->getCategoriesHierarchically();
        $producents = Producent::all();
        list($products, $filters, $sorts) = $this->applyPaginationAndFiltering(Product::query());
        
        return view('admin.products.index', compact('products', 'filters', 'sorts', 'categories', 'producents'));
    }

    public function create()
    {
        $categories = Category::all();
        $producents = Producent::all();
        return view('admin.products.create', compact('categories', 'producents'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'sku' => 'required|unique:products,sku',
            'name' => 'required',
            'price' => 'required|numeric',
            'price_old' => 'nullable|numeric',
            'promo_price' => 'nullable|numeric|lt:price_old',
            'promo_from' => 'nullable|date',
            'promo_to' => 'nullable|date|after_or_equal:promo_from',
            // Add other validation rules as necessary
        ]);

        $product = Product::create([
            'sku' => $request->input('sku'),
            'name' => $request->input('name'),
            'description' => $request->input('description'),
            'short_description' => $request->input('short_description'),
            'price' => $request->input('price'),
            'price_old' => $request->input('price_old'),
            'promo_price' => $request->input('promo_price'),
            'promo_from' => $request->input('promo_from'),
            'promo_to' => $request->input('promo_to'),
            'stock' => $request->input('stock'),
            'weight' => $request->input('weight'),
            'category_id' => $request->input('category_id'),
            'producent_id' => $request->input('producent_id'),
            'ean' => $request->input('ean'),
            'status' => $request->input('status') ? 1 : 0,
            'unit' => $request->input('unit'),
        ]);

        return redirect()->route('admin.products.index')->with('success', 'Product created successfully.');
    }

    public function edit(Product $product)
    {
        dd($product->toArray());
        // Get all properties that are either not tied to a category or tied to the product's category or parent categories
        $category = $product->category;
        $categoryIds = $category ? array_merge([$category->id], $category->getAncestorIds()) : [];
        
        $properties = Property::whereDoesntHave('categories')
                        ->orWhereHas('categories', function($query) use ($categoryIds) {
                            $query->whereIn('categories.id', $categoryIds);
                        })->get();

        $categories = Category::getCategoriesHierarchically();
        $producents = Producent::all();
        // dd($product->properties()->get());

        return view('admin.products.edit', compact('product', 'categories', 'producents', 'properties'));
    }

    private function getCategoriesHierarchically($parentId = null, $level = 0)
    {
        $categories = Category::where('parent_id', $parentId)->orderBy('name')->get();
        $result = [];

        foreach ($categories as $category) {
            $category->name = str_repeat('--', $level) . ' ' . $category->name;
            $result[] = $category;
            $result = array_merge($result, $this->getCategoriesHierarchically($category->id, $level + 1));
        }

        return $result;
    }

    public function update(Request $request, Product $product)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category_id' => 'required|exists:categories,id',
            'producent_id' => 'required|exists:producents,id',
            'price_percentage' => 'nullable|integer|min:0|max:1000',
            'main_image' => 'nullable|image|max:2048',
            'gallery_images.*' => 'nullable|image|max:2048',
        ]);

        $oldPercentage = $product->price_percentage;

        $product->update($request->only([
            'name',
            'description',
            'category_id',
            'producent_id',
            'price_percentage',
        ]));

        // Update price if percentage changed
        if ($oldPercentage !== $request->price_percentage) {
            $product->updatePriceFromPercentage();
        }

        // Handle main image
        if ($request->hasFile('main_image')) {
            $product->clearMediaCollection('images');
            $product->addMedia($request->file('main_image'))->toMediaCollection('images');
        }

        // Handle gallery images
        if ($request->hasFile('gallery_images')) {
            foreach ($request->file('gallery_images') as $image) {
                $product->addMedia($image)->toMediaCollection('gallery');
            }
        }

        // Save property values
        if ($request->has('properties')) {
            foreach ($request->properties as $propertyId => $propertyData) {
                $propertyOptionId = $propertyData['property_option_id'] ?? null;
                $value = $propertyData['value'] ?? null;

                $product->properties()->syncWithoutDetaching([
                    $propertyId => [
                        'property_option_id' => $propertyOptionId,
                        'value' => $value,
                    ]
                ]);
            }
        }

        if ($request->input('action') === 'save') {
            return redirect()->route('admin.products.index')->with('success', 'Product saved and redirected to list.');
        }

        return redirect()->route('admin.products.edit', $product->id)->with('success', 'Product updated successfully.');
    }

    public function destroy(Product $product)
    {
        $product->delete();
        return redirect()->route('admin.products.index')->with('success', 'Product deleted successfully.');
    }

    public function search(Request $request)
    {
        $search = $request->get('q');
        $excludeIds = $request->get('exclude', []);

        // Ensure $excludeIds is an array
        if (!is_array($excludeIds)) {
            $excludeIds = explode(',', $excludeIds);
        }

        $products = Product::where('name', 'like', '%' . $search . '%')
            ->whereNotIn('id', $excludeIds)
            ->limit(10)
            ->get();

        return response()->json($products->map(function ($product) {
            return ['id' => $product->id, 'text' => $product->name];
        }));
    }

    public function deleteMedia($mediaId)
    {
        $media = Media::findOrFail($mediaId);
        if ($media->model_type === Product::class) {
            $media->delete();
            return response()->json(['success' => true]);
        }
        return response()->json(['success' => false], 403);
    }

    public function massMove(Request $request)
    {
        dd($request->all());
        $request->validate([
            'product_ids' => 'required|array',
            'product_ids.*' => 'exists:products,id',
            'target_category_id' => 'required|exists:categories,id'
        ]);
        dd($request->all());

        $products = Product::whereIn('id', $request->product_ids)->get();
        $targetCategory = Category::findOrFail($request->target_category_id);

        foreach ($products as $product) {
            $product->update(['category_id' => $targetCategory->id]);
        }

        return redirect()->back()
            ->with('success', 'Products have been successfully moved to ' . $targetCategory->name);
    }
}
