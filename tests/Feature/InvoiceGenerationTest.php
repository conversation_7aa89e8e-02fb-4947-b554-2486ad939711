<?php

namespace Tests\Feature;

use App\Models\Order;
use App\Models\Invoice;
use App\Services\InvoiceService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class InvoiceGenerationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $invoiceService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->invoiceService = app(InvoiceService::class);
        Storage::fake('public');
    }

    /**
     * Test invoice generation for an order
     */
    public function test_can_generate_invoice_for_order(): void
    {
        // Create a test order with items
        $order = Order::factory()->create([
            'total' => 123.00,
            'subtotal' => 100.00,
            'delivery_cost' => 23.00,
        ]);

        // Create order items
        $order->items()->create([
            'product_id' => 1,
            'name' => 'Test Product',
            'quantity' => 2,
            'price' => 50.00,
        ]);

        // Generate invoice
        $invoice = $this->invoiceService->generateInvoice($order);

        // Assert invoice was created
        $this->assertInstanceOf(Invoice::class, $invoice);
        $this->assertEquals($order->id, $invoice->order_id);
        $this->assertNotEmpty($invoice->invoice_number);
        $this->assertEquals($order->total, $invoice->total);
        $this->assertEquals('draft', $invoice->status);
    }

    /**
     * Test invoice number generation
     */
    public function test_invoice_number_generation(): void
    {
        $order1 = Order::factory()->create();
        $order2 = Order::factory()->create();

        $invoice1 = $this->invoiceService->generateInvoice($order1);
        $invoice2 = $this->invoiceService->generateInvoice($order2);

        // Assert unique invoice numbers
        $this->assertNotEquals($invoice1->invoice_number, $invoice2->invoice_number);
        $this->assertStringContainsString('FV/', $invoice1->invoice_number);
        $this->assertStringContainsString('FV/', $invoice2->invoice_number);
    }

    /**
     * Test PDF generation
     */
    public function test_can_generate_pdf(): void
    {
        $order = Order::factory()->create();
        $order->items()->create([
            'product_id' => 1,
            'name' => 'Test Product',
            'quantity' => 1,
            'price' => 100.00,
        ]);

        $invoice = $this->invoiceService->generateInvoice($order);
        $pdfPath = $this->invoiceService->generatePdf($invoice);

        // Assert PDF was generated
        $this->assertNotEmpty($pdfPath);
        $this->assertTrue(Storage::disk('public')->exists($pdfPath));

        // Refresh invoice to get updated data
        $invoice->refresh();
        $this->assertTrue($invoice->hasPdf());
        $this->assertNotNull($invoice->pdf_generated_at);
    }

    /**
     * Test invoice data structure
     */
    public function test_invoice_contains_required_data(): void
    {
        $order = Order::factory()->create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'phone' => '+48123456789',
        ]);

        $invoice = $this->invoiceService->generateInvoice($order);

        // Assert seller data
        $this->assertArrayHasKey('company_name', $invoice->seller_data);
        $this->assertArrayHasKey('address', $invoice->seller_data);
        $this->assertArrayHasKey('tax_id', $invoice->seller_data);

        // Assert buyer data
        $this->assertEquals('John Doe', $invoice->buyer_data['name']);
        $this->assertEquals('<EMAIL>', $invoice->buyer_data['email']);
        $this->assertArrayHasKey('address', $invoice->buyer_data);

        // Assert dates
        $this->assertNotNull($invoice->issue_date);
        $this->assertNotNull($invoice->sale_date);
        $this->assertNotNull($invoice->due_date);
    }
}
