<?php

namespace Tests;

use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication;

    /**
     * Setup the test environment.
     */
    protected function setUp(): void
    {
        parent::setUp();

        // Ensure we're using the test database
        $this->ensureTestDatabase();

        // Set up fake storage for testing
        Storage::fake('public');
        Storage::fake('local');
    }

    /**
     * Ensure we're using the test database connection.
     * This is a safety check to prevent accidental data corruption.
     */
    protected function ensureTestDatabase(): void
    {
        $connection = DB::getDefaultConnection();
        $database = DB::connection()->getDatabaseName();

        // For SQLite in-memory database, the database name will be ':memory:'
        // For file-based SQLite, it should contain 'testing'
        if ($connection !== 'sqlite_testing' &&
            $database !== ':memory:' &&
            !str_contains($database, 'testing')) {
            throw new \Exception(
                "Tests must use the test database. Current connection: {$connection}, database: {$database}"
            );
        }
    }

    /**
     * Clean up after each test.
     */
    protected function tearDown(): void
    {
        // Clear any fake storage
        Storage::fake('public');
        Storage::fake('local');

        parent::tearDown();
    }
}
