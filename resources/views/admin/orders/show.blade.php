@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-bold">Order Details #{{ $order->uuid }}</h2>
        <a href="{{ route('admin.orders.index') }}" class="text-blue-500 hover:text-blue-700">&larr; Back to Orders</a>
    </div>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {{ session('error') }}
        </div>
    @endif

    <!-- Order Status -->
    <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2">Order Status</h3>
        <form action="{{ route('admin.orders.update-status', $order) }}" method="POST" class="flex gap-4">
            @csrf
            <select name="status" class="border rounded p-2">
                <option value="pending" {{ $order->status === 'pending' ? 'selected' : '' }}>Pending</option>
                <option value="processing" {{ $order->status === 'processing' ? 'selected' : '' }}>Processing</option>
                <option value="completed" {{ $order->status === 'completed' ? 'selected' : '' }}>Completed</option>
                <option value="cancelled" {{ $order->status === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
            </select>
            <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded">Update Status</button>
        </form>
    </div>

    <!-- Payment Status -->
    <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2">Payment Status</h3>
        <div class="bg-blue-50 p-4 rounded flex items-center justify-between">
            <div>
                <p class="text-blue-700 mb-1">
                    <strong>Payment Method:</strong> {{ ucfirst($order->payment_method) }}
                </p>
                <p class="text-{{ $order->payment_status === 'paid' ? 'green' : 'orange' }}-700">
                    <strong>Status:</strong> {{ ucfirst($order->payment_status) }}
                </p>
                @if($order->payment_checked_at)
                    <p class="text-sm text-gray-600">Last checked: {{ $order->payment_checked_at->format('Y-m-d H:i:s') }}</p>
                @endif
                @if($order->payu_order_id)
                    <p class="text-sm text-gray-600">PayU Order ID: {{ $order->payu_order_id }}</p>
                @endif
            </div>
            <div>
                @if($order->payment_method === 'payu')
                    <form action="{{ route('admin.orders.check-payment', $order) }}" method="POST" class="inline">
                        @csrf
                        <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                            Check Payment Status
                        </button>
                    </form>
                @endif
            </div>
        </div>
    </div>

    <!-- InPost Label Section -->
    @if($order->deliveryAddress && $order->deliveryAddress->deliveryMethod && $order->deliveryAddress->deliveryMethod->api_provider === 'inpost')
        <div class="mb-6">
            <h3 class="text-lg font-semibold mb-2">InPost Shipping Label</h3>
            <div class="bg-blue-50 p-4 rounded flex items-center justify-between">
                <div>
                    <p class="text-blue-700 mb-1">
                        <strong>Delivery Type:</strong> 
                        {{ $order->deliveryAddress->deliveryMethod->name }}
                        @if($order->deliveryAddress->deliveryMethod->type === 'point')
                            (Paczkomat)
                        @else
                            (Courier)
                        @endif
                    </p>
                    @if($order->inpost_shipment_id)
                        <p class="text-green-700"><strong>Status:</strong> Label generated (ID: {{ $order->inpost_shipment_id }})</p>
                        @if($order->inpost_status)
                            <p class="text-sm text-gray-600">InPost Status: {{ $order->inpost_status }}</p>
                        @endif
                        @if($order->inpost_tracking_number)
                            <p class="text-green-700" id="tracking-info">
                                <strong>Tracking Number(s):</strong>
                                <span id="tracking-numbers">{{ $order->inpost_tracking_number }}</span>
                            </p>
                        @else
                            <p class="text-orange-700" id="no-tracking-info">
                                <strong>Status:</strong> Label generated but not purchased yet
                            </p>
                        @endif
                    @else
                        <p class="text-orange-700"><strong>Status:</strong> Label not generated</p>
                    @endif
                </div>
                <div class="flex gap-2">
                    @if($order->inpost_shipment_id)
                        @if($order->inpost_tracking_number)
                            <!-- Download Label Button -->
                            <a href="{{ route('admin.orders.download-inpost-label', $order) }}"
                               class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                                Download Label
                            </a>
                        @else
                            <!-- Purchase Label Button -->
                            <button id="purchase-label-btn"
                                    class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600"
                                    onclick="purchaseLabel({{ $order->id }})">
                                Purchase Label
                            </button>
                        @endif
                        <!-- Regenerate Label Button -->
                        <form action="{{ route('admin.orders.generate-inpost-label', $order) }}" method="POST" class="inline">
                            @csrf
                            <button type="submit"
                                    class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600"
                                    onclick="return confirm('Are you sure you want to regenerate the label? This will create a new shipment.')">
                                Regenerate Label
                            </button>
                        </form>
                    @else
                        <!-- Generate Label Button -->
                        <form action="{{ route('admin.orders.generate-inpost-label', $order) }}" method="POST" class="inline">
                            @csrf
                            <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                                Generate InPost Label
                            </button>
                        </form>
                    @endif
                </div>
            </div>
        </div>
    @endif

    <!-- Invoice Section -->
    <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2">Invoice / Faktura</h3>
        <div class="bg-blue-50 p-4 rounded flex items-center justify-between">
            <div>
                @if($order->invoice)
                    <p class="text-green-700 mb-1">
                        <strong>Invoice Number:</strong> {{ $order->invoice->invoice_number }}
                    </p>
                    <p class="text-blue-700 mb-1">
                        <strong>Issue Date:</strong> {{ $order->invoice->issue_date->format('d.m.Y') }}
                    </p>
                    <p class="text-blue-700">
                        <strong>Status:</strong> {{ ucfirst($order->invoice->status) }}
                    </p>
                    @if($order->invoice->hasPdf())
                        <p class="text-sm text-gray-600">PDF generated: {{ $order->invoice->pdf_generated_at->format('Y-m-d H:i:s') }}</p>
                    @endif
                @else
                    <p class="text-orange-700"><strong>Status:</strong> Invoice not generated</p>
                @endif
            </div>
            <div class="flex gap-2">
                @if($order->invoice)
                    @if($order->invoice->hasPdf())
                        <!-- Download Invoice Button -->
                        <a href="{{ route('admin.orders.download-invoice', $order) }}"
                           class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                            Download Invoice PDF
                        </a>
                        <!-- Regenerate PDF Button -->
                        <form action="{{ route('admin.orders.regenerate-invoice-pdf', $order) }}" method="POST" class="inline">
                            @csrf
                            <button type="submit"
                                    class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600"
                                    onclick="return confirm('Are you sure you want to regenerate the invoice PDF?')">
                                Regenerate PDF
                            </button>
                        </form>
                    @else
                        <!-- Generate PDF Button -->
                        <form action="{{ route('admin.orders.generate-invoice', $order) }}" method="POST" class="inline">
                            @csrf
                            <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                                Generate Invoice PDF
                            </button>
                        </form>
                    @endif
                @else
                    <!-- Generate and Download Button -->
                    <a href="{{ route('admin.orders.generate-download-invoice', $order) }}"
                       class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                        Generate & Download Invoice
                    </a>
                @endif
            </div>
        </div>
    </div>

    <div class="grid grid-cols-2 gap-6 mb-6">
        <!-- Customer Information -->
        <div>
            <h3 class="text-lg font-semibold mb-2">Customer Information</h3>
            <p><strong>Name:</strong> {{ $order->user->first_name }} {{ $order->user->last_name }}</p>
            <p><strong>Email:</strong> {{ $order->user->email }}</p>
            <p><strong>Phone:</strong> {{ $order->user->phone }}</p>
        </div>

        <!-- Order Information -->
        <div>
            <h3 class="text-lg font-semibold mb-2">Order Information</h3>
            <p><strong>Date:</strong> {{ $order->created_at->format('Y-m-d H:i') }}</p>
            <p><strong>Payment Method:</strong> {{ ucfirst($order->payment_method) }}</p>
            <p><strong>Payment Status:</strong> 
                <span class="px-2 py-1 rounded text-sm {{ $order->payment_status === 'paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                    {{ ucfirst($order->payment_status) }}
                </span>
            </p>
            @if($order->deliveryAddress && $order->deliveryAddress->deliveryMethod)
                <p><strong>Delivery Method:</strong> {{ $order->deliveryAddress->deliveryMethod->name }}</p>
            @endif
        </div>
    </div>

    <!-- Delivery Address -->
    <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2">Delivery Address</h3>
        @if($order->deliveryAddress)
            @if($order->deliveryAddress->deliveryMethod && $order->deliveryAddress->deliveryMethod->type === 'point')
                <!-- Paczkomat/Point delivery -->
                <div class="bg-blue-50 p-4 rounded">
                    <p><strong>Delivery Type:</strong> {{ $order->deliveryAddress->deliveryMethod->name }}</p>
                    @if($order->deliveryAddress->point_id)
                        <p><strong>Point ID:</strong> {{ $order->deliveryAddress->point_id }}</p>
                    @endif
                    @if($order->deliveryAddress->point_address)
                        <p><strong>Address:</strong> {{ $order->deliveryAddress->point_address }}</p>
                    @endif
                    @if($order->deliveryAddress->point_data)
                        <div class="mt-2">
                            <strong>Additional Point Data:</strong>
                            <pre class="text-sm bg-gray-100 p-2 rounded mt-1">{{ json_encode($order->deliveryAddress->point_data, JSON_PRETTY_PRINT) }}</pre>
                        </div>
                    @endif
                </div>
            @else
                <!-- Standard courier delivery -->
                <div class="bg-gray-50 p-4 rounded">
                    @if($order->deliveryAddress->deliveryMethod)
                        <p><strong>Delivery Type:</strong> {{ $order->deliveryAddress->deliveryMethod->name }}</p>
                    @endif
                    @if($order->deliveryAddress->name)
                        <p><strong>Address Name:</strong> {{ $order->deliveryAddress->name }}</p>
                    @endif
                    <p><strong>Street:</strong> {{ $order->deliveryAddress->street }} {{ $order->deliveryAddress->building_number }}{{ $order->deliveryAddress->apartment_number ? '/' . $order->deliveryAddress->apartment_number : '' }}</p>
                    <p><strong>City:</strong> {{ $order->deliveryAddress->post_code }} {{ $order->deliveryAddress->city }}</p>
                    @if($order->deliveryAddress->phone)
                        <p><strong>Phone:</strong> {{ $order->deliveryAddress->phone }}</p>
                    @endif
                </div>
            @endif
        @else
            <p class="text-gray-500">No delivery address available</p>
        @endif
    </div>

    <!-- Billing Address (if different from delivery) -->
    @if($order->billing_address && is_array($order->billing_address))
        <div class="mb-6">
            <h3 class="text-lg font-semibold mb-2">Billing Address</h3>
            <div class="bg-gray-50 p-4 rounded">
                @if(isset($order->billing_address['name']))
                    <p><strong>Address Name:</strong> {{ $order->billing_address['name'] }}</p>
                @endif
                <p><strong>Street:</strong> {{ $order->billing_address['street'] ?? '' }} {{ $order->billing_address['building_number'] ?? '' }}{{ isset($order->billing_address['apartment_number']) && $order->billing_address['apartment_number'] ? '/' . $order->billing_address['apartment_number'] : '' }}</p>
                <p><strong>City:</strong> {{ $order->billing_address['post_code'] ?? '' }} {{ $order->billing_address['city'] ?? '' }}</p>
            </div>
        </div>
    @endif

    <!-- Order Items -->
    <div>
        <h3 class="text-lg font-semibold mb-2">Order Items</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="py-3 px-4 border-b text-left">Product</th>
                        <th class="py-3 px-4 border-b text-center">Quantity</th>
                        <th class="py-3 px-4 border-b text-right">Unit Price</th>
                        <th class="py-3 px-4 border-b text-right">Total</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($order->items as $item)
                    <tr class="hover:bg-gray-50">
                        <td class="py-3 px-4 border-b">
                            <div>
                                <p class="font-medium">{{ $item->product->name }}</p>
                                @if($item->product->sku)
                                    <p class="text-sm text-gray-500">SKU: {{ $item->product->sku }}</p>
                                @endif
                            </div>
                        </td>
                        <td class="py-3 px-4 border-b text-center">{{ $item->quantity }}</td>
                        <td class="py-3 px-4 border-b text-right">{{ number_format($item->price, 2) }} PLN</td>
                        <td class="py-3 px-4 border-b text-right font-medium">{{ number_format($item->price * $item->quantity, 2) }} PLN</td>
                    </tr>
                    @endforeach
                </tbody>
                <tfoot class="bg-gray-50">
                    <tr>
                        <td colspan="2" class="py-3 px-4 text-right font-semibold">Subtotal:</td>
                        <td class="py-3 px-4 text-right font-semibold">{{ number_format($order->items->sum(fn($item) => $item->price * $item->quantity), 2) }} PLN</td>
                        <td class="py-3 px-4"></td>
                    </tr>
                    @if($order->deliveryAddress && $order->deliveryAddress->deliveryMethod)
                        <tr>
                            <td colspan="2" class="py-3 px-4 text-right font-semibold">Delivery ({{ $order->deliveryAddress->deliveryMethod->name }}):</td>
                            <td class="py-3 px-4 text-right font-semibold">{{ number_format($order->deliveryAddress->deliveryMethod->price, 2) }} PLN</td>
                            <td class="py-3 px-4"></td>
                        </tr>
                    @endif
                    <tr class="border-t-2">
                        <td colspan="2" class="py-3 px-4 text-right font-bold text-lg">Total:</td>
                        <td class="py-3 px-4 text-right font-bold text-lg">{{ number_format($order->total, 2) }} PLN</td>
                        <td class="py-3 px-4"></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>

<script>
function purchaseLabel(orderId) {
    const button = document.getElementById('purchase-label-btn');
    const originalText = button.textContent;

    // Disable button and show loading state
    button.disabled = true;
    button.textContent = 'Preparing offers...';
    button.classList.add('opacity-50', 'cursor-not-allowed');

    // Show progress message
    showMessage('Preparing InPost offers and purchasing label. This may take up to 30 seconds...', 'info');

    // Update button text to show purchasing phase
    setTimeout(() => {
        if (button.disabled) {
            button.textContent = 'Purchasing label...';
        }
    }, 5000);

    // Make AJAX request
    fetch(`/admin/orders/${orderId}/inpost-label/purchase`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            showMessage('Label purchased successfully!', 'success');

            // Update UI to show tracking numbers
            const noTrackingInfo = document.getElementById('no-tracking-info');
            if (noTrackingInfo) {
                noTrackingInfo.style.display = 'none';
            }

            // Show tracking info
            const trackingInfo = document.getElementById('tracking-info');
            if (trackingInfo) {
                trackingInfo.style.display = 'block';
                document.getElementById('tracking-numbers').textContent = data.tracking_numbers_string;
            } else {
                // Create tracking info element if it doesn't exist
                const trackingElement = document.createElement('p');
                trackingElement.className = 'text-green-700';
                trackingElement.id = 'tracking-info';
                trackingElement.innerHTML = `<strong>Tracking Number(s):</strong> <span id="tracking-numbers">${data.tracking_numbers_string}</span>`;
                noTrackingInfo.parentNode.insertBefore(trackingElement, noTrackingInfo.nextSibling);
            }

            // Replace Purchase button with Download button
            button.outerHTML = `
                <a href="/admin/orders/${orderId}/inpost-label/download"
                   class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    Download Label
                </a>
            `;
        } else {
            // Show error message
            showMessage(data.message || 'Failed to purchase label', 'error');

            // Re-enable button
            button.disabled = false;
            button.textContent = originalText;
            button.classList.remove('opacity-50', 'cursor-not-allowed');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('An error occurred while purchasing the label', 'error');

        // Re-enable button
        button.disabled = false;
        button.textContent = originalText;
        button.classList.remove('opacity-50', 'cursor-not-allowed');
    });
}

function showMessage(message, type) {
    // Remove existing messages
    const existingMessages = document.querySelectorAll('.alert-message');
    existingMessages.forEach(msg => msg.remove());

    // Create new message element
    const messageDiv = document.createElement('div');
    let className = 'alert-message px-4 py-3 rounded mb-4 ';
    if (type === 'success') {
        className += 'bg-green-100 border border-green-400 text-green-700';
    } else if (type === 'info') {
        className += 'bg-blue-100 border border-blue-400 text-blue-700';
    } else {
        className += 'bg-red-100 border border-red-400 text-red-700';
    }
    messageDiv.className = className;
    messageDiv.textContent = message;

    // Insert message at the top of the content
    const contentDiv = document.querySelector('.bg-white.p-6.rounded.shadow-md');
    const firstChild = contentDiv.children[1]; // After the header
    contentDiv.insertBefore(messageDiv, firstChild);

    // Auto-remove message after 5 seconds
    setTimeout(() => {
        messageDiv.remove();
    }, 5000);
}
</script>
@endsection
