<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
>
    <testsuites>
        <testsuite name="Unit">
            <directory>tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory>tests/Feature</directory>
        </testsuite>
    </testsuites>
    <source>
        <include>
            <directory>app</directory>
        </include>
    </source>
    <php>
        <!-- Environment Configuration -->
        <env name="APP_ENV" value="testing"/>
        <env name="APP_DEBUG" value="false"/>

        <!-- Database Configuration - Force SQLite for testing -->
        <env name="DB_CONNECTION" value="sqlite_testing"/>
        <env name="DB_DATABASE" value=":memory:"/>

        <!-- Performance Optimizations for Testing -->
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="CACHE_DRIVER" value="array"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="QUEUE_CONNECTION" value="sync"/>

        <!-- Disable External Services -->
        <env name="MAIL_MAILER" value="array"/>
        <env name="SCOUT_DRIVER" value="null"/>
        <env name="PULSE_ENABLED" value="false"/>
        <env name="TELESCOPE_ENABLED" value="false"/>

        <!-- Logging Configuration -->
        <env name="LOG_CHANNEL" value="single"/>
        <env name="LOG_LEVEL" value="emergency"/>

        <!-- Disable External APIs -->
        <env name="PAYU_ENVIRONMENT" value="testing"/>
        <env name="INPOST_ENVIRONMENT" value="testing"/>
    </php>
</phpunit>
